"""
Solar & Wind Energy Generation Dashboard
Main Streamlit Application
"""

import streamlit as st
from helper.setup_logger import setup_logger
import base64


# Import login components
from frontend.ui_components.login import (
    initialize_session_state,
    is_authenticated,
    login_form,
    add_logout_button,
    apply_login_css
)

# Import dashboard components
from frontend.ui_components.dashboard_controls import (
    create_client_plant_filters,
    create_date_filters,
    setup_page,
    apply_custom_css,
    create_universal_plot_options
)

# Import display functions
from frontend.display_plots.summary_display import (
    display_generation_vs_consumption,
    display_generation_only,
    display_consumption_only,
    display_consumption_and_generation_pie,
    display_generation_vs_consumption_hourly
)
from frontend.display_plots.tod_display import (
    display_monthly_tod_before_banking,
    display_monthly_banking_settlement,
    display_tod_generation_vs_consumption,
    display_tod_generation,
    display_tod_consumption,
    display_mean_trend_vs_irregularities,
    display_monthly_settled_heatmap
)

from frontend.display_plots.power_cost_display import (
    display_power_cost_analysis
)

# Import data management
from backend.data.db_data_manager import load_client_data
from db.db_setup import CONN


logging = setup_logger("app", "app.log")

def get_base64_image(image_path):
    """Convert image to base64 string"""
    try:
        with open(image_path, "rb") as img_file:
            logging.info(f"Successfully loaded image: {image_path}")
            return base64.b64encode(img_file.read()).decode()
    except FileNotFoundError:
        logging.warning(f"Image file not found: {image_path}")
        return None
    except PermissionError:
        logging.error(f"Permission denied when accessing image: {image_path}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error loading image {image_path}: {str(e)}")
        return None

def main():
    """Main application function"""
    
    try:
        # Setup page configuration
        logging.info("Starting application initialization")
        setup_page()
        logging.info("Page configuration completed")
        
        # Initialize session state for authentication
        initialize_session_state()
        logging.info("Session state initialized")
        
        # Check if user is authenticated
        if not is_authenticated():
            logging.info("User not authenticated, showing login form")
            login_form()
            return
        
        logging.info("User authenticated successfully")
        
        # Apply custom CSS for dashboard
        apply_custom_css()
        
    except Exception as e:
        logging.error(f"Error during application initialization: {str(e)}")
        st.error("⚠️ We're having trouble starting the application. Please refresh the page and try again.")
        return
    
    try:
        # Load client data
        logging.info("Loading client data from database")
        with st.spinner("Loading client data..."):
            client_data = load_client_data()
        
        if not client_data:
            logging.warning("No client data found in database")
            st.warning("📊 No client data found. Please contact support if this issue persists.")
            return
        
        logging.info(f"Successfully loaded data for {len(client_data)} clients")
        
    except Exception as e:
        logging.error(f"Failed to load client data: {str(e)}")
        st.error("❌ Unable to load client data. Please check your connection and try again.")
        return
    
    try:
        # Create sidebar controls
        logo_base64 = get_base64_image("logo/logo_integrum.jpg")
        if logo_base64 is None:
            logging.warning("Logo image could not be loaded, using default icon")
        
        logo_html = f'<img src="data:image/jpeg;base64,{logo_base64}" style="height: 24px; width: 24px; vertical-align: middle; margin-right: 8px;"/>' if logo_base64 else '🎛️'
        
        st.sidebar.markdown(f"""
        <div style='background: linear-gradient(90deg, #1E88E5, #42A5F5); 
                    padding: 1rem; margin: -1rem -1rem 1rem -1rem; 
                    border-radius: 0.5rem;'>
            <h2 style='color: white; margin: 0; text-align: center;'>
                {logo_html}
                Dashboard Controls
            </h2>
        </div>
        """, unsafe_allow_html=True)
        
        st.sidebar.markdown("---")
        
        # Client and Plant Selection
        try:
            selected_client, selected_plant, plant_type = create_client_plant_filters(client_data)
            logging.info(f"Client selection: {selected_client}, Plant: {selected_plant}, Type: {plant_type}")
        except Exception as e:
            logging.error(f"Error creating client/plant filters: {str(e)}")
            st.error("⚠️ Unable to load client selection options. Please refresh the page.")
            return
        
        st.sidebar.markdown("---")
        
        # Date Selection
        try:
            start_date, end_date = create_date_filters()
            logging.info(f"Date range selected: {start_date} to {end_date}")
        except Exception as e:
            logging.error(f"Error creating date filters: {str(e)}")
            st.error("⚠️ Unable to load date selection options. Please refresh the page.")
            return
        
        st.sidebar.markdown("---")
        
        # Universal Plot Options (only created once)
        try:
            create_universal_plot_options()
        except Exception as e:
            logging.warning(f"Error creating plot options: {str(e)}")
            # This is not critical, continue without showing error to user
        
        # Add logout button to sidebar
        try:
            add_logout_button()
        except Exception as e:
            logging.warning(f"Error adding logout button: {str(e)}")
            # This is not critical, continue without showing error to user
        
        # Main content area
        if selected_client:
            
            # Create tabs
            try:
                tab1, tab2, tab3 = st.tabs(["Summary", "Generation and consumption", "Bill"])
                logging.info("Created main application tabs")
            except Exception as e:
                logging.error(f"Error creating tabs: {str(e)}")
                st.error("⚠️ Unable to create dashboard tabs. Please refresh the page.")
                return
            
            with tab1:
                try:
                    # Determine what to pass to display functions
                    display_name = selected_plant if selected_plant else selected_client
                    logging.info(f"Summary tab: Displaying data for {display_name}")
                    
                    # Show database connection status
                    if CONN is None:
                        logging.error("Database connection is None in Summary tab")
                        st.error("❌ Unable to connect to the database. Please contact support if this issue persists.")
                        st.stop()
                    
                     # Automatically display all plots
                    st.subheader("Generation vs Consumption")
                    col1, col2 = st.columns([5, 2])
                    with col2:
                        granularity = st.radio(
                            "Granularity",
                            options=["Daily", "Hourly"],
                            index=0,
                            horizontal=True,
                            label_visibility="collapsed"
                        )
                    is_hourly = granularity.lower() == "hourly"
                    
                    try:
                        with st.spinner(f"Loading {granularity.lower()} generation vs consumption data..."):
                            display_generation_vs_consumption(display_name, start_date, end_date, is_hourly_aggregation=is_hourly)
                        logging.info(f"Successfully displayed generation vs consumption chart for {display_name}")
                    except Exception as e:
                        logging.error(f"Error displaying generation vs consumption: {str(e)}")
                        st.error("⚠️ Unable to load generation vs consumption data. Please try selecting a different date range.")


                    # try:
                    #     st.subheader("TOD Monthly – after banking")
                    #     with st.spinner("Loading banking settlement data..."):
                    #         display_monthly_banking_settlement(display_name)
                    #     logging.info(f"Successfully displayed monthly banking settlement for {display_name}")
                    # except Exception as e:
                    #     logging.error(f"Error displaying monthly banking settlement: {str(e)}")
                    #     st.error("⚠️ Unable to load banking settlement data. Please try again later.")
                    
                    # st.markdown("---")

                    # try:
                    #     st.subheader("ToD Generation Analysis")
                    #     with st.spinner("Loading ToD generation data..."):
                    #         display_tod_generation(display_name, start_date, end_date)
                    #     logging.info(f"Successfully displayed ToD generation analysis for {display_name}")
                    # except Exception as e:
                    #     logging.error(f"Error displaying ToD generation analysis: {str(e)}")
                    #     st.error("⚠️ Unable to load ToD generation data. Please try selecting a different date range.")
                    
                    # st.markdown("---")
                    
                    # try:
                    #     st.subheader("ToD Consumption Analysis")
                    #     with st.spinner("Loading ToD consumption data..."):
                    #         display_tod_consumption(display_name, start_date, end_date)
                    #     logging.info(f"Successfully displayed ToD consumption analysis for {display_name}")
                    # except Exception as e:
                    #     logging.error(f"Error displaying ToD consumption analysis: {str(e)}")
                    #     st.error("⚠️ Unable to load ToD consumption data. Please try selecting a different date range.")
                    
                    # st.markdown("---")

                    # try:
                    #     st.subheader("Generation vs Consumption Hourly")
                    #     with st.spinner("Loading hourly generation vs consumption data..."):
                    #         display_generation_vs_consumption_hourly(display_name, start_date, end_date)
                    #     logging.info(f"Successfully displayed hourly generation vs consumption chart for {display_name}")
                    # except Exception as e:
                    #     logging.error(f"Error displaying hourly generation vs consumption: {str(e)}")
                    #     st.error("⚠️ Unable to load hourly data. Please try selecting a different date range.")

                    # st.markdown("---")

                    
                except Exception as e:
                    logging.error(f"Error in Summary tab: {str(e)}")
                    st.error("⚠️ An error occurred while loading the summary data. Please refresh the page and try again.")
            
            with tab2:
                try:
                    logging.info(f"ToD Analysis tab: Displaying data for {display_name}")
                    
                    # Show database connection status
                    if CONN is None:
                        logging.error("Database connection is None in ToD Analysis tab")
                        st.error("❌ Unable to connect to the database. Please contact support if this issue persists.")
                        st.stop()

                    st.subheader("Monthly - Last 12 months and TODwise.")
                    
                    
                    
                    
                    # try:
                    #     st.subheader("Monthly TOD")
                    #     with st.spinner("Loading ToD comparison data..."):
                    #         display_tod_generation_vs_consumption(display_name, start_date, end_date)
                    #     logging.info(f"Successfully displayed ToD generation vs consumption for {display_name}")
                    # except Exception as e:
                    #     logging.error(f"Error displaying ToD generation vs consumption: {str(e)}")
                    #     st.error("⚠️ Unable to load ToD comparison data. Please try selecting a different date range.")
                    
                    # st.markdown("---")
                    
                    
                    
                    
                        
                except Exception as e:
                    logging.error(f"Error in ToD Analysis tab: {str(e)}")
                    st.error("⚠️ An error occurred while loading the ToD analysis data. Please refresh the page and try again.")
            
            # with tab3:
                # try:
                #     logging.info(f"Power Cost Analysis tab: Displaying data for {display_name}")
                #     st.header("💰 Power Cost Analysis")
                    
                #     try:
                #         display_power_cost_analysis(display_name)
                #         logging.info(f"Successfully displayed power cost analysis for {display_name}")
                #     except Exception as e:
                #         logging.error(f"Error displaying power cost analysis: {str(e)}")
                #         st.error("⚠️ Unable to load power cost analysis. This feature may not be available for your selected client.")
                        
                # except Exception as e:
                #     logging.error(f"Error in Power Cost Analysis tab: {str(e)}")
                #     st.error("⚠️ An error occurred while loading the power cost analysis. Please refresh the page and try again.")
        
        else:
            # No content when no selection is made
            logging.info("No client selected, showing welcome message")
            st.info("👋 Welcome! Please select a client from the sidebar to view your energy data.")
    
    except Exception as e:
        logging.error(f"Critical application error: {str(e)}")
        st.error("❌ We encountered an unexpected error. Please refresh the page and try again.")
        
        # Log additional details for debugging but don't show to user
        logging.error(f"Error context - Selected client: {locals().get('selected_client', 'None')}")
        logging.error(f"Error context - Selected plant: {locals().get('selected_plant', 'None')}")
        logging.error(f"Error context - Date range: {locals().get('start_date', 'None')} to {locals().get('end_date', 'None')}")
        
        # Show user-friendly help message
        st.info("💡 **Troubleshooting Tips:**")
        st.info("- Try refreshing the page")
        st.info("- Check your internet connection")
        st.info("- Try selecting a different client or date range")
        st.info("- If the problem persists, please contact support")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        logging.error(f"Fatal application error: {str(e)}")
        st.error("❌ A critical error occurred. Please refresh the page or contact support if the problem persists.")
        st.stop()