import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.ticker import FuncFormatter
import pandas as pd
from matplotlib.patches import Patch
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import numpy as np
from .tod_config import SLOT_METADATA, get_slot_color_map, add_slot_labels_with_time

from helper.setup_logger import setup_logger

logging = setup_logger("summary_tab_visual", "summary_tab_visual.log")



def format_thousands(x, _):
    """Format numbers with thousands separators"""
    try:
        return f'{int(x):,}'
    except (ValueError, TypeError) as e:
        logging.warning(f"Error formatting number {x}: {str(e)}")
        return str(x) if x is not None else '0'
    except Exception as e:
        logging.error(f"Unexpected error in format_thousands: {str(e)}")
        return '0'

def get_hour_slot(hour):
    """Map hour (0-23) to time slot."""
    try:
        if not isinstance(hour, (int, float)):
            logging.warning(f"Invalid hour type {type(hour)}: {hour}")
            return "Day (Normal)"
        
        hour = int(hour)
        if not (0 <= hour <= 23):
            logging.warning(f"Hour value out of range (0-23): {hour}")
            return "Day (Normal)"
        
        if 6 <= hour < 9:
            return "Morning Peak"
        elif 9 <= hour < 18:
            return "Day (Normal)"
        elif 18 <= hour < 22:
            return "Evening Peak"
        else:  # 22-23 and 0-5
            return "Night Off-Peak"
    except Exception as e:
        logging.error(f"Error determining hour slot for {hour}: {str(e)}")
        return "Day (Normal)"

def get_hour_color(hour, metric_type='generation'):
    """Get color for hour based on time slot and metric type."""
    try:
        slot = get_hour_slot(hour)
        
        if slot not in SLOT_METADATA:
            logging.warning(f"Slot {slot} not found in metadata, using default color")
            base_color = "#1f77b4"  # Default blue color
        else:
            base_color = SLOT_METADATA[slot]["color"]
        
        # For consumption, make it slightly darker/different
        if metric_type == 'consumption':
            try:
                # Convert hex to RGB, darken it, then back to hex
                hex_color = base_color.lstrip('#')
                rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                # Darken by reducing each RGB component by 30
                darkened_rgb = tuple(max(0, c - 30) for c in rgb)
                return f"#{darkened_rgb[0]:02x}{darkened_rgb[1]:02x}{darkened_rgb[2]:02x}"
            except ValueError as e:
                logging.warning(f"Error converting hex color {base_color}: {str(e)}")
                return "#d62728"  # Default red color for consumption
        
        return base_color
    except Exception as e:
        logging.error(f"Error getting hour color for {hour}: {str(e)}")
        return "#1f77b4" if metric_type == 'generation' else "#d62728"

#####Generation VS Consumption


def plot_generation_vs_consumption(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str,
    is_hourly_aggregation: bool = False
):
    """Area plot for generation vs. consumption with optional settled percentage plot."""
    
    try:
        logging.info(f"Creating generation vs consumption plot for {plant_display_name} ({start_date} to {end_date})")
        
        if df is None or df.empty:
            logging.warning(f"No data available for generation vs consumption plot - {plant_display_name}")
            return None
        
        # Validate required columns
        required_columns = ['generation', 'consumption']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns in generation vs consumption data: {missing_columns}")
            return None
        
        is_single_day = start_date == end_date
        
    except Exception as e:
        logging.error(f"Error initializing generation vs consumption plot for {plant_display_name}: {str(e)}")
        return None

    try:
        # Compute surplus columns if missing
        if 'surplus_generation' not in df.columns:
            df['surplus_generation'] = df.apply(lambda row: max(0, row['generation'] - row['consumption']), axis=1)
        if 'surplus_demand' not in df.columns:
            df['surplus_demand'] = df.apply(lambda row: max(0, row['consumption'] - row['generation']), axis=1)
        
        logging.info(f"Successfully calculated surplus columns for {plant_display_name}")
        
    except Exception as e:
        logging.error(f"Error calculating surplus columns for {plant_display_name}: {str(e)}")
        return None

    try:
        # Create figure and axes
        if is_hourly_aggregation:
            fig, axes = plt.subplots(2, 1, figsize=(16, 12), gridspec_kw={'height_ratios': [3, 1]})
            ax = axes[0]
        elif is_single_day:
            fig, ax = plt.subplots(figsize=(16, 8))
            axes = [ax]
        else:
            fig, axes = plt.subplots(2, 1, figsize=(16, 12), gridspec_kw={'height_ratios': [3, 1]})
            ax = axes[0]
            
        logging.info(f"Successfully created figure for {plant_display_name}")
        
    except Exception as e:
        logging.error(f"Error creating figure for {plant_display_name}: {str(e)}")
        return None

    try:
        # Determine x-axis
        if is_hourly_aggregation:
            if 'datetime' not in df.columns:
                logging.error(f"Datetime column missing for hourly aggregation - {plant_display_name}")
                return None
            x = df['datetime']
            ax.set_xlabel("Datetime (Hourly)", fontsize=12)
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %d\n%H:%M'))
            ax.xaxis.set_major_locator(mdates.AutoDateLocator())
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right', fontweight='semibold', fontsize=10, color='dimgray', fontfamily='sans-serif')
        elif is_single_day:
            if 'datetime' not in df.columns:
                logging.error(f"Datetime column missing for single day plot - {plant_display_name}")
                return None
            x = df['datetime']
            ax.set_xlabel("Time of Day", fontsize=12)
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
        else:
            if 'date' not in df.columns:
                logging.error(f"Date column missing for multi-day plot - {plant_display_name}")
                return None
            x = df['date']
            ax.set_xlabel("Date", fontsize=12)
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2) if len(df) > 30 else mdates.DayLocator(interval=2))
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

        # Main area plots
        ax.plot(x, df['generation'], color='green', linewidth=2.5, marker='o', markersize=6, label='Generation')
        ax.plot(x, df['consumption'], color='red', linewidth=2.5, marker='s', markersize=6, linestyle='--', label='Consumption')

        ax.fill_between(x, df['generation'], df['consumption'], where=(df['generation'] >= df['consumption']), color='green', alpha=0.3, interpolate=True, label='Surplus Generation')
        ax.fill_between(x, df['generation'], df['consumption'], where=(df['generation'] < df['consumption']), color='red', alpha=0.3, interpolate=True, label='Surplus Demand')
        
        logging.info(f"Successfully created main plots for {plant_display_name}")
        
    except KeyError as e:
        logging.error(f"Missing required column for plotting {plant_display_name}: {str(e)}")
        return None
    except Exception as e:
        logging.error(f"Error creating main plots for {plant_display_name}: {str(e)}")
        return None

    # Title
    label = (
        f"Hourly Aggregation: {start_date} to {end_date}" if is_hourly_aggregation
        else start_date if is_single_day
        else f"{start_date} to {end_date}"
    )
    ax.set_title(f"Generation vs Consumption for {plant_display_name} ({label})", fontsize=16, fontweight='bold', pad=20)
    ax.set_ylabel("Energy (kWh)", fontsize=12)

    # Dynamic y-axis formatting
    if df[['generation', 'consumption']].max().max() > 1000:
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x/1000:.1f}K' if x >= 1000 else f'{x:.0f}'))

    ax.legend(loc='upper right', fontsize=11)
    ax.grid(True, linestyle='--', alpha=0.4)

    try:
        # Settled percentage subplot
        if (is_hourly_aggregation and len(axes) > 1) or (not is_single_day and not is_hourly_aggregation and len(axes) > 1):
            ax2 = axes[1]

            if 'settled' in df.columns:
                df['settled_percentage'] = df.apply(
                    lambda row: 100 * row['settled'] / row['consumption'] if row['consumption'] > 0 else 0,
                    axis=1
                )

                if is_hourly_aggregation:
                    ax2.plot(df['datetime'], df['settled_percentage'], color='blue', linewidth=2.5, marker='d', markersize=6, label='Settled %')
                    ax2.set_xlabel("Datetime (Hourly)", fontsize=12)
                    ax2.set_title("Hourly Settled Percentage (Without Banking)", fontsize=14, fontweight='bold')
                    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%b %d\n%H:%M'))
                else:
                    ax2.plot(df['date'], df['settled_percentage'], color='blue', linewidth=2.5, marker='d', markersize=6, label='Settled %')
                    ax2.set_xlabel("Date", fontsize=12)
                    ax2.set_title("Daily Settled Percentage (Without Banking)", fontsize=14, fontweight='bold')
                    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))
                    ax2.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2) if len(df) > 30 else mdates.DayLocator(interval=2))

                ax2.set_ylabel("Settled (%)", fontsize=12)
                ax2.grid(True, linestyle='--', alpha=0.4)
                ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f'{x:.0f}%'))
                ax2.set_ylim(0, max(120, df['settled_percentage'].max() * 1.1))
                ax2.legend(loc='upper right', fontsize=10)
                plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
            else:
                logging.warning(f"Settled column not found for {plant_display_name}, skipping settled percentage plot")

        plt.tight_layout()
        if len(axes) > 1:
            plt.subplots_adjust(hspace=0.3)

        logging.info(f"Successfully completed generation vs consumption plot for {plant_display_name}")
        return fig
        
    except Exception as e:
        logging.error(f"Error finalizing generation vs consumption plot for {plant_display_name}: {str(e)}")
        return fig  # Return figure even if finalization fails


def plot_generation_vs_consumption_interactive(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str,
    is_hourly_aggregation: bool = False
):
    """Interactive Plotly version of generation vs consumption plot with hover functionality."""
    try:
        logging.info(f"Creating interactive generation vs consumption plot for {plant_display_name} ({start_date} to {end_date})")
        
        if df is None or df.empty:
            logging.warning(f"No data available for interactive generation vs consumption plot - {plant_display_name}")
            return None

        # Validate required columns
        required_columns = ['generation', 'consumption']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns in interactive generation vs consumption data: {missing_columns}")
            return None

        is_single_day = start_date == end_date
        
    except Exception as e:
        logging.error(f"Error initializing interactive generation vs consumption plot for {plant_display_name}: {str(e)}")
        return None
    
    # Calculate surplus generation and demand if not already present
    if 'surplus_generation' not in df.columns:
        df['surplus_generation'] = df.apply(lambda row: max(0, row['generation'] - row['consumption']), axis=1)
    if 'surplus_demand' not in df.columns:
        df['surplus_demand'] = df.apply(lambda row: max(0, row['consumption'] - row['generation']), axis=1)
    
    # Create the figure
    if is_single_day or is_hourly_aggregation:
        if is_hourly_aggregation:
            fig = make_subplots(
                rows=1, cols=1,
                subplot_titles=[f"Hourly Aggregation Generation vs Consumption for {plant_display_name} ({start_date} to {end_date})"]
            )
            x_col = 'datetime'
            x_title = "Datetime (Hourly)"
        else:  # is_single_day
            fig = make_subplots(
                rows=1, cols=1,
                subplot_titles=[f"Generation vs Consumption for {plant_display_name} ({start_date})"]
            )
            x_col = 'datetime'
            x_title = "Time of Day"
    else:
        fig = make_subplots(
            rows=2, cols=1,
            row_heights=[0.7, 0.3],
            subplot_titles=[
                f"Generation vs Consumption for {plant_display_name} ({start_date} to {end_date})",
                "Daily Settled Percentage (Without Banking)"
            ],
            vertical_spacing=0.08
        )
        x_col = 'date'
        x_title = "Date"
    
    # Prepare data for hover
    df_hover = df.copy()
    df_hover['surplus_generation_display'] = df_hover['surplus_generation'].round(2)
    df_hover['surplus_demand_display'] = df_hover['surplus_demand'].round(2)
    df_hover['difference'] = df_hover['generation'] - df_hover['consumption']
    
    # Calculate additional metrics for hover
    df_hover['generation_mwh'] = df_hover['generation'] / 1000
    df_hover['consumption_mwh'] = df_hover['consumption'] / 1000
    df_hover['difference_mwh'] = df_hover['difference'] / 1000
    
    # Calculate efficiency metrics
    df_hover['self_consumption_rate'] = np.minimum(df_hover['generation'], df_hover['consumption']) / df_hover['generation'] * 100
    df_hover['self_consumption_rate'] = df_hover['self_consumption_rate'].fillna(0)
    
    df_hover['energy_independence'] = np.minimum(df_hover['generation'], df_hover['consumption']) / df_hover['consumption'] * 100
    df_hover['energy_independence'] = df_hover['energy_independence'].fillna(0)
    
    # Settlement data
    if 'settled' in df_hover.columns:
        df_hover['settled_mwh'] = df_hover['settled'] / 1000
        df_hover['settlement_efficiency'] = (df_hover['settled'] / np.minimum(df_hover['generation'], df_hover['consumption']) * 100).fillna(0)
    else:
        df_hover['settled_mwh'] = 0
        df_hover['settlement_efficiency'] = 0
    
    # Format datetime/date/hour for display
    if is_hourly_aggregation:
        df_hover['datetime_display'] = pd.to_datetime(df_hover['datetime']).dt.strftime('%b %d %H:%M')
        hover_template_base = '<b>⏰ Datetime:</b> %{customdata[0]}<br>'
    elif is_single_day:
        df_hover['time_display'] = pd.to_datetime(df_hover['datetime']).dt.strftime('%H:%M')
        hover_template_base = '<b>🕐 Time:</b> %{customdata[0]}<br>'
    else:
        df_hover['date_display'] = pd.to_datetime(df_hover['date']).dt.strftime('%Y-%m-%d (%a)')
        hover_template_base = '<b>📅 Date:</b> %{customdata[0]}<br>'
    
    # Create custom data for hover
    if is_hourly_aggregation:
        customdata = df_hover[['datetime_display', 'generation', 'consumption', 'difference', 
                              'surplus_generation_display', 'surplus_demand_display',
                              'generation_mwh', 'consumption_mwh', 'difference_mwh',
                              'self_consumption_rate', 'energy_independence', 'settled_mwh', 'settlement_efficiency']].values
    elif is_single_day:
        customdata = df_hover[['time_display', 'generation', 'consumption', 'difference', 
                              'surplus_generation_display', 'surplus_demand_display',
                              'generation_mwh', 'consumption_mwh', 'difference_mwh',
                              'self_consumption_rate', 'energy_independence', 'settled_mwh', 'settlement_efficiency']].values
    else:
        customdata = df_hover[['date_display', 'generation', 'consumption', 'difference', 
                              'surplus_generation_display', 'surplus_demand_display',
                              'generation_mwh', 'consumption_mwh', 'difference_mwh',
                              'self_consumption_rate', 'energy_independence', 'settled_mwh', 'settlement_efficiency']].values
    
    # Generation line
    fig.add_trace(
        go.Scatter(
            x=df_hover[x_col],
            y=df_hover['generation'],
            mode='lines+markers',
            name='Generation',
            line=dict(color='green', width=3),
            marker=dict(size=8, symbol='circle'),
            customdata=customdata,
            hovertemplate=hover_template_base +
                         '<b>⚡ Generation:</b> %{customdata[1]:,.0f} kWh (%{customdata[6]:.1f} MWh)<br>' +
                         '<b>🔌 Consumption:</b> %{customdata[2]:,.0f} kWh (%{customdata[7]:.1f} MWh)<br>' +
                         '<b>⚖️ Net Difference:</b> %{customdata[3]:+,.0f} kWh (%{customdata[8]:+.1f} MWh)<br>' +
                         '<b>📈 Surplus Generation:</b> %{customdata[4]:,.0f} kWh<br>' +
                         '<b>📉 Surplus Demand:</b> %{customdata[5]:,.0f} kWh<br>' +
                         '<b> Settlement:</b> %{customdata[11]:.1f} MWh (%{customdata[12]:.1f}% efficiency)<br>' +
                         '<extra></extra>'
        ),
        row=1, col=1
    )
    
    # Consumption line
    fig.add_trace(
        go.Scatter(
            x=df_hover[x_col],
            y=df_hover['consumption'],
            mode='lines+markers',
            name='Consumption',
            line=dict(color='red', width=3, dash='dash'),
            marker=dict(size=8, symbol='square'),
            customdata=customdata,
            hovertemplate=hover_template_base +
                         '<b>⚡ Generation:</b> %{customdata[1]:,.0f} kWh (%{customdata[6]:.1f} MWh)<br>' +
                         '<b>🔌 Consumption:</b> %{customdata[2]:,.0f} kWh (%{customdata[7]:.1f} MWh)<br>' +
                         '<b>⚖️ Net Difference:</b> %{customdata[3]:+,.0f} kWh (%{customdata[8]:+.1f} MWh)<br>' +
                         '<b>📈 Surplus Generation:</b> %{customdata[4]:,.0f} kWh<br>' +
                         '<b>📉 Surplus Demand:</b> %{customdata[5]:,.0f} kWh<br>' +
                         '<b>💰 Settlement:</b> %{customdata[11]:.1f} MWh (%{customdata[12]:.1f}% efficiency)<br>' +
                         '<extra></extra>'
        ),
        row=1, col=1
    )
    
    # Fill between for surplus generation (green area)
    fig.add_trace(
        go.Scatter(
            x=df_hover[x_col],
            y=df_hover['generation'],
            fill=None,
            mode='lines',
            line=dict(color='rgba(0,0,0,0)'),
            showlegend=False,
            hoverinfo='skip'
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=df_hover[x_col],
            y=df_hover['consumption'],
            fill='tonexty',
            fillcolor='rgba(0, 128, 0, 0.3)',
            mode='lines',
            line=dict(color='rgba(0,0,0,0)'),
            name='Surplus Generation',
            hoverinfo='skip'
        ),
        row=1, col=1
    )
    
    # Fill between for surplus demand (red area)
    # We need to create a trace that only fills where consumption > generation
    consumption_fill = df_hover['consumption'].where(df_hover['consumption'] > df_hover['generation'], df_hover['generation'])
    
    fig.add_trace(
        go.Scatter(
            x=df_hover[x_col],
            y=df_hover['generation'],
            fill=None,
            mode='lines',
            line=dict(color='rgba(0,0,0,0)'),
            showlegend=False,
            hoverinfo='skip'
        ),
        row=1, col=1
    )
    
    fig.add_trace(
        go.Scatter(
            x=df_hover[x_col],
            y=consumption_fill,
            fill='tonexty',
            fillcolor='rgba(255, 0, 0, 0.3)',
            mode='lines',
            line=dict(color='rgba(0,0,0,0)'),
            name='Surplus Demand',
            hoverinfo='skip'
        ),
        row=1, col=1
    )
    
    # Add settled percentage plot for multiple days
    if not is_single_day and not is_hourly_aggregation:
        # Compute settled percentage safely
        df_hover['settled_percentage'] = df_hover.apply(
            lambda row: 100 * row['settled'] / row['consumption'] if row['consumption'] > 0 else 0,
            axis=1
        )
        
        # Custom data for settled percentage plot
        settled_customdata = []
        for _, row in df_hover.iterrows():
            settled_customdata.append([
                row['date_display'], 
                row['settled_percentage'],
                row['settled'] if 'settled' in row and pd.notna(row['settled']) else 0,
                row['consumption'],
                row['generation']
            ])
        
        fig.add_trace(
            go.Scatter(
                x=df_hover[x_col],
                y=df_hover['settled_percentage'],
                mode='lines+markers',
                name='Settled %',
                line=dict(color='blue', width=3),
                marker=dict(size=8, symbol='diamond'),
                customdata=settled_customdata,
                hovertemplate='<b>📅 Date:</b> %{customdata[0]}<br>' +
                             '<b>🟢 Settled Percentage:</b> %{customdata[1]:.1f}%<br>' +
                             '<b>💰 Settled Energy:</b> %{customdata[2]:,.0f} kWh<br>' +
                             '<b>🔌 Total Consumption:</b> %{customdata[3]:,.0f} kWh<br>' +
                             '<b>⚡ Total Generation:</b> %{customdata[4]:,.0f} kWh<br>' +
                             '<extra></extra>'
            ),
            row=2, col=1
        )
    
    # Update layout
    fig.update_layout(
        height=600 if (is_single_day or is_hourly_aggregation) else 800,
        showlegend=True,
        hovermode='x unified',
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.08,  # Increased padding between title and legend
            xanchor="right",
            x=1
        ),
        margin=dict(t=80)  # Add top margin to accommodate the legend spacing
    )
    
    # Update x-axis
    fig.update_xaxes(title_text=x_title, row=1, col=1)
    if is_hourly_aggregation:
        # For hourly aggregation, show datetime with proper formatting
        fig.update_xaxes(
            tickformat='%b %d<br>%H:%M',
            row=1, col=1
        )
    elif not is_single_day and not is_hourly_aggregation:
        fig.update_xaxes(title_text="Date", row=2, col=1)
    
    # Update y-axis
    fig.update_yaxes(title_text="Energy (kWh)", row=1, col=1)
    if not is_single_day:
        max_settled_percentage = df_hover['settled_percentage'].max() if 'settled_percentage' in df_hover.columns else 100
        y_max = max(120, max_settled_percentage * 1.1)
        fig.update_yaxes(title_text="Settled (%)", range=[0, y_max], row=2, col=1)
    
    # Add grid
    fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)')
    fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='rgba(128,128,128,0.2)')
    
    return fig






def plot_generation_vs_consumption_hourly(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str,
    is_hourly_aggregation: bool = False
):
    """
    Line plot for generation vs. consumption with time-of-day colored grid lines and area fills for hourly aggregation.
    
    When is_hourly_aggregation=True, this function applies time-based colors to:
    1. Vertical grid lines at time slot transition points (6am, 9am, 6pm, 10pm)
    2. Area fills between generation and consumption lines for each time segment
    
    Time slots defined in tod_config.py:
    - Morning Peak (6am-9am): Orange
    - Day Normal (9am-6pm): Sky Blue  
    - Evening Peak (6pm-10pm): Crimson Red
    - Night Off-Peak (10pm-6am): Deep Purple
    
    Features for hourly aggregation:
    - Original green/red line colors maintained
    - Colored vertical grid lines at time slot transitions
    - Colored area fills for each time segment
    - Enhanced visual clarity for peak/off-peak periods
    """
    if df.empty:
        print("⚠️ No data available to plot.")
        return

    is_single_day = start_date == end_date

    if 'surplus_generation' not in df.columns:
        df['surplus_generation'] = df.apply(lambda row: max(0, row['generation'] - row['consumption']), axis=1)
    if 'surplus_demand' not in df.columns:
        df['surplus_demand'] = df.apply(lambda row: max(0, row['consumption'] - row['generation']), axis=1)

    if is_single_day or is_hourly_aggregation:
        fig, ax = plt.subplots(figsize=(16, 8))
        axes = [ax]
    else:
        fig, axes = plt.subplots(2, 1, figsize=(16, 12), gridspec_kw={'height_ratios': [3, 1]})
        ax = axes[0]

    # Main area plot - handle different data types
    if is_single_day:
        x = df['datetime']
        ax.set_xlabel("Time of Day", fontsize=12)
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    elif is_hourly_aggregation:
        # For hourly aggregation, create hour labels
        x = df['hour']
        ax.set_xlabel("Hour of Day (across all selected days)", fontsize=12)
        # Set hour labels from 0 to 23 with bold and inclined styling
        ax.set_xticks(range(0, 24))
        ax.set_xticklabels([f"{h:02d}:00" for h in range(24)])
        # plt.setp(ax.get_xticklabels(), rotation=30, ha='right', fontweight='bold')
        plt.setp(
    ax.get_xticklabels(),
    rotation=30,
    ha='right',
    fontweight='semibold',
    fontsize=11,
    color='dimgray',
    fontfamily='sans-serif',
)

    else:
        x = df['date']
        ax.set_xlabel("Date", fontsize=12)
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))
        if len(df) > 30:
            ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        else:
            ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))

    # Plot original lines (keep original colors)
    ax.plot(x, df['generation'], color='green', linewidth=2.5, marker='o', markersize=6, label='Generation')
    ax.plot(x, df['consumption'], color='red', linewidth=2.5, marker='s', markersize=6, linestyle='--', label='Consumption')

    # Apply time-of-day color scheme to grid lines and area fills for hourly aggregation
    if is_hourly_aggregation:
        # Add colored vertical grid lines only at time slot transitions
        transition_hours = [6, 9, 18, 22]  # Where time slots change
        
        for hour in transition_hours:
            if hour < len(df):
                slot = get_hour_slot(hour)
                slot_color = SLOT_METADATA[slot]["color"]
                
                # Add vertical grid line at transition points with time-slot color
                ax.axvline(x=hour, color=slot_color, linestyle='-', alpha=0.8, linewidth=2.5)
        
        # Fill areas with time-slot colors hour by hour for perfect continuity
        for hour in range(len(df) - 1):
            # Get current hour's time slot and color
            slot = get_hour_slot(hour)
            slot_color = SLOT_METADATA[slot]["color"]
            
            # Get current and next hour data
            current_gen = df.iloc[hour]['generation']
            current_cons = df.iloc[hour]['consumption']
            next_gen = df.iloc[hour + 1]['generation']
            next_cons = df.iloc[hour + 1]['consumption']
            
            # Fill between for this hour segment
            ax.fill_between([hour, hour + 1], 
                           [current_gen, next_gen], 
                           [current_cons, next_cons],
                           color=slot_color, alpha=0.3, interpolate=True)
        
        # Keep the regular horizontal grid
        ax.grid(True, axis='y', linestyle='--', alpha=0.4)
    else:
        # Original fill areas for non-hourly aggregation
        ax.fill_between(x, df['generation'], df['consumption'], 
                        where=(df['generation'] >= df['consumption']), 
                        color='green', alpha=0.3, interpolate=True, label='Surplus Generation')
        ax.fill_between(x, df['generation'], df['consumption'], 
                        where=(df['generation'] < df['consumption']), 
                        color='red', alpha=0.3, interpolate=True, label='Surplus Demand')

    # Update title for hourly aggregation
    if is_hourly_aggregation:
        label = f"Hourly Aggregation: {start_date} to {end_date}"
    else:
        label = start_date if is_single_day else f"{start_date} to {end_date}"
    
    ax.set_title(f"Generation vs Consumption for {plant_display_name} ({label})", 
                 fontsize=16, fontweight='bold', pad=20)

    ax.set_ylabel("Energy (kWh)", fontsize=12)

    if df[['generation', 'consumption']].max().max() > 1000:
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1000:.1f}K' if x >= 1000 else f'{x:.0f}'))

    # Simple legend for all cases
    ax.legend(loc='upper right', fontsize=11)
    
    # Add standard grid only for non-hourly aggregation (hourly has colored grid lines)
    if not is_hourly_aggregation:
        ax.grid(True, linestyle='--', alpha=0.4)
    
    # Only rotate labels for date plots, not for hourly
    if not is_hourly_aggregation:
        plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

    # Add settled percentage plot only for daily aggregation (not hourly)
    if not is_single_day and not is_hourly_aggregation and len(axes) > 1:
        ax2 = axes[1]

        # Compute settled percentage safely
        df['settled_percentage'] = df.apply(
            lambda row: 100 * row['settled'] / row['consumption'] if row['consumption'] > 0 else 0,
            axis=1
        )

        ax2.plot(df['date'], df['settled_percentage'], color='blue', linewidth=2.5, marker='d', markersize=6, label='Settled %')

        ax2.set_xlabel("Date", fontsize=12)
        ax2.set_ylabel("Settled (%)", fontsize=12)
        ax2.set_title("Daily Settled Percentage (Without Banking)", fontsize=14, fontweight='bold')
        ax2.grid(True, linestyle='--', alpha=0.4)

        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%b %d'))
        if len(df) > 30:
            ax2.xaxis.set_major_locator(mdates.WeekdayLocator(interval=2))
        else:
            ax2.xaxis.set_major_locator(mdates.DayLocator(interval=2))

        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:.0f}%'))
        ax2.set_ylim(0, max(120, df['settled_percentage'].max() * 1.1))

        ax2.legend(loc='upper right', fontsize=10)
        plt.setp(
    ax2.get_xticklabels(),
    rotation=30,
    ha='right',
    fontweight='semibold',
    fontsize=11,
    color='dimgray',
    fontfamily='sans-serif',
)


    plt.tight_layout()
    if not is_single_day and not is_hourly_aggregation and len(axes) > 1:
        plt.subplots_adjust(hspace=0.3)

    return fig


def plot_generation_vs_consumption_hourly_interactive(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str,
    is_hourly_aggregation: bool = False
):
    """
    Interactive Plotly version of generation vs. consumption with time-of-day colored areas and enhanced hover functionality.
    
    When is_hourly_aggregation=True, this function applies time-based colors to:
    1. Area fills between generation and consumption lines for each time segment
    2. Interactive hover showing time slot information
    3. Colored vertical lines at time slot transitions (6am, 9am, 6pm, 10pm)
    
    Time slots defined in tod_config.py:
    - Morning Peak (6am-9am): Orange
    - Day Normal (9am-6pm): Sky Blue  
    - Evening Peak (6pm-10pm): Crimson Red
    - Night Off-Peak (10pm-6am): Deep Purple
    
    Features for hourly aggregation:
    - Interactive time-slot colored area fills
    - Enhanced hover with time slot information
    - Colored vertical lines at time slot transitions
    - Responsive design with zoom and pan functionality
    """
    
    try:
        logging.info(f"Creating interactive hourly generation vs consumption plot for {plant_display_name} ({start_date} to {end_date})")
        
        if df is None or df.empty:
            logging.warning(f"No data available for interactive hourly generation vs consumption plot - {plant_display_name}")
            return None

        # Validate required columns
        required_columns = ['generation', 'consumption']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns in interactive hourly generation vs consumption data: {missing_columns}")
            return None

        is_single_day = start_date == end_date
        
        # Calculate surplus generation and demand if not already present
        if 'surplus_generation' not in df.columns:
            df['surplus_generation'] = df.apply(lambda row: max(0, row['generation'] - row['consumption']), axis=1)
        if 'surplus_demand' not in df.columns:
            df['surplus_demand'] = df.apply(lambda row: max(0, row['consumption'] - row['generation']), axis=1)
        
        # Add time slot information to dataframe for hover
        if is_hourly_aggregation and 'hour' in df.columns:
            df['time_slot'] = df['hour'].apply(get_hour_slot)
            df['time_slot_color'] = df['hour'].apply(lambda h: SLOT_METADATA[get_hour_slot(h)]["color"])
            df['formatted_hour'] = df['hour'].apply(lambda h: f"{h:02d}:00")
        
        # Create simple figure
        fig = go.Figure()
        
        # Determine x-axis data and labels
        if is_single_day:
            x_data = df['datetime']
            x_title = "Time of Day"
            hover_template = "<b>Time:</b> %{x|%H:%M}<br><b>Generation:</b> %{y:.1f} kWh<br><b>Consumption:</b> %{customdata[0]:.1f} kWh<br><b>Surplus Gen:</b> %{customdata[1]:.1f} kWh<br><b>Surplus Demand:</b> %{customdata[2]:.1f} kWh<extra></extra>"
        elif is_hourly_aggregation:
            x_data = df['hour']
            x_title = "Hour of Day (across all selected days)"
            hover_template = "<b>Hour:</b> %{customdata[3]}<br><b>Time Slot:</b> %{customdata[4]}<br><b>Generation:</b> %{y:.1f} kWh<br><b>Consumption:</b> %{customdata[0]:.1f} kWh<br><b>Surplus Gen:</b> %{customdata[1]:.1f} kWh<br><b>Surplus Demand:</b> %{customdata[2]:.1f} kWh<extra></extra>"
        else:
            x_data = df['date']
            x_title = "Date"
            hover_template = "<b>Date:</b> %{x}<br><b>Generation:</b> %{y:.1f} kWh<br><b>Consumption:</b> %{customdata[0]:.1f} kWh<br><b>Surplus Gen:</b> %{customdata[1]:.1f} kWh<br><b>Surplus Demand:</b> %{customdata[2]:.1f} kWh<extra></extra>"
        
        # Prepare custom data for hover
        if is_hourly_aggregation:
            custom_data = np.column_stack([
                df['consumption'],
                df['surplus_generation'],
                df['surplus_demand'],
                df['formatted_hour'],
                df['time_slot']
            ])
        else:
            custom_data = np.column_stack([
                df['consumption'],
                df['surplus_generation'],
                df['surplus_demand']
            ])
        
        # Apply time-of-day color scheme and area fills - matching the matplotlib version exactly
        if is_hourly_aggregation:
            # Add colored vertical grid lines only at time slot transitions
            transition_hours = [6, 9, 18, 22]  # Where time slots change
            
            for hour in transition_hours:
                if hour < len(df):
                    slot = get_hour_slot(hour)
                    slot_color = SLOT_METADATA[slot]["color"]
                    
                    # Add vertical grid line at transition points with time-slot color
                    fig.add_vline(
                        x=hour,
                        line=dict(color=slot_color, width=2.5, dash='solid'),
                        opacity=0.8
                    )
            
            # Fill areas with time-slot colors hour by hour for perfect continuity
            # Keep track of which slots we've added to legend
            slots_added_to_legend = set()
            
            for hour in range(len(df) - 1):
                # Get current hour's time slot and color
                slot = get_hour_slot(hour)
                slot_color = SLOT_METADATA[slot]["color"]
                
                # Get current and next hour data
                current_gen = df.iloc[hour]['generation']
                current_cons = df.iloc[hour]['consumption']
                next_gen = df.iloc[hour + 1]['generation']
                next_cons = df.iloc[hour + 1]['consumption']
                            
                # Create fill_between equivalent in Plotly - area between generation and consumption
                # Add to legend only for the first occurrence of each slot
                show_in_legend = slot not in slots_added_to_legend
                if show_in_legend:
                    slots_added_to_legend.add(slot)
                
                fig.add_trace(go.Scatter(
                    x=[hour, hour + 1, hour + 1, hour],
                    y=[current_gen, next_gen, next_cons, current_cons],
                    fill='toself',
                    fillcolor=slot_color,
                    opacity=0.3,
                    line=dict(width=0),
                    showlegend=show_in_legend,
                    hoverinfo='skip',
                    name=f'{slot} ({SLOT_METADATA[slot]["time"]})',
                    legendgroup=slot
                ))
            
            # Set custom x-axis ticks for hourly aggregation
            fig.update_xaxes(
                tickmode='array',
                tickvals=list(range(0, 24)),
                ticktext=[f"{h:02d}:00" for h in range(24)],
                tickangle=-30
            )
        else:
            # Original fill areas for non-hourly aggregation
            # Surplus generation (where generation >= consumption)
            surplus_gen_indices = df['generation'] >= df['consumption']
            if surplus_gen_indices.any():
                # Create base line for generation
                fig.add_trace(go.Scatter(
                    x=x_data,
                    y=df['generation'],
                    fill=None,
                    mode='lines',
                    line=dict(width=0),
                    showlegend=False,
                    hoverinfo='skip'
                ))
                
                # Create fill to consumption line where generation is higher
                fig.add_trace(go.Scatter(
                    x=x_data,
                    y=df['consumption'],
                    fill='tonexty',
                    fillcolor='rgba(0, 128, 0, 0.3)',  # Green with transparency
                    mode='lines',
                    line=dict(width=0),
                    name='Surplus Generation',
                    showlegend=True,
                    hoverinfo='skip'
                ))
            
            # Surplus demand (where consumption > generation)
            surplus_demand_indices = df['generation'] < df['consumption']
            if surplus_demand_indices.any():
                # Create base line for consumption
                fig.add_trace(go.Scatter(
                    x=x_data,
                    y=df['consumption'],
                    fill=None,
                    mode='lines',
                    line=dict(width=0),
                    showlegend=False,
                    hoverinfo='skip'
                ))
                
                # Create fill to generation line where consumption is higher
                fig.add_trace(go.Scatter(
                    x=x_data,
                    y=df['generation'],
                    fill='tonexty',
                    fillcolor='rgba(255, 0, 0, 0.3)',  # Red with transparency
                    mode='lines',
                    line=dict(width=0),
                    name='Surplus Demand',
                    showlegend=True,
                    hoverinfo='skip'
                ))
        
        # Add generation line (keep original colors)
        fig.add_trace(go.Scatter(
            x=x_data,
            y=df['generation'],
            mode='lines+markers',
            name='Generation',
            line=dict(color='green', width=2.5),
            marker=dict(size=6, symbol='circle'),
            customdata=custom_data,
            hovertemplate=hover_template
        ))
        
        # Add consumption line (keep original colors)
        fig.add_trace(go.Scatter(
            x=x_data,
            y=df['consumption'],
            mode='lines+markers',
            name='Consumption',
            line=dict(color='red', width=2.5, dash='dash'),
            marker=dict(size=6, symbol='square'),
            customdata=custom_data,
            hovertemplate=hover_template.replace('Generation', 'Consumption')
        ))
        
        # Update title for hourly aggregation
        if is_hourly_aggregation:
            label = f"Hourly Aggregation: {start_date} to {end_date}"
        else:
            label = start_date if is_single_day else f"{start_date} to {end_date}"
        
        fig.update_layout(
            title=f"Generation vs Consumption for {plant_display_name} ({label})",
            xaxis_title=x_title,
            yaxis_title="Energy (kWh)",
            hovermode='x unified',
            template='plotly_white',
            height=600,
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,  # Legend positioned below the title
                xanchor="right",
                x=1
            ),
            margin=dict(t=140)  # Increased top margin to push title up and create space for legend
        )
        
        # Dynamic y-axis formatting for large values
        max_value = df[['generation', 'consumption']].max().max()
        if max_value > 1000:
            fig.update_yaxes(tickformat='.1s')
        
        logging.info(f"Successfully created interactive hourly generation vs consumption plot for {plant_display_name}")
        return fig
        
    except Exception as e:
        logging.error(f"Error creating interactive hourly generation vs consumption plot for {plant_display_name}: {str(e)}")
        return None


##Generation

def create_generation_only_plot(df, plant_name, start_date, end_date=None, is_hourly_aggregation=False):
    if df.empty or 'generation' not in df.columns:
        fig, ax = plt.subplots(figsize=(10, 5))
        ax.text(0.5, 0.5, "No data available", ha='center', va='center', fontsize=12)
        return fig

    is_single_day = end_date is None or start_date == end_date
    fig, ax = plt.subplots(figsize=(12, 6))

    if is_single_day:
        x = df['datetime']
        ax.plot(x, df['generation'], color='green', marker='o', linewidth=2)
        ax.set_xlabel("Time of Day")
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
    elif is_hourly_aggregation:
        # For hourly aggregation, use hour as x-axis
        x = df['hour']
        bars = ax.bar(x, df['generation'], color='green', alpha=0.8)

        # Add values inside bars rotated 90 degrees
        for bar in bars:
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2,
                height * 0.5,
                f"{int(height):,}",
                ha='center',
                va='center',
                fontsize=9,
                rotation=90,
                color='white'
            )

        ax.set_xlabel("Hour of Day (across all selected days)")
        ax.set_xticks(range(0, 24))
        ax.set_xticklabels([f"{h:02d}:00" for h in range(24)])
        # plt.setp(ax.get_xticklabels(), rotation=30, ha='right', fontweight='bold')
        plt.setp(
    ax.get_xticklabels(),
    rotation=30,
    ha='right',
    fontweight='semibold',
    fontsize=11,
    color='dimgray',
    fontfamily='sans-serif',
)

    else:
        x = df['date']
        bars = ax.bar(x, df['generation'], color='green', alpha=0.8)

        # Add values inside bars rotated 90 degrees
        for bar in bars:
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2,
                height * 0.5,
                f"{int(height):,}",
                ha='center',
                va='center',
                fontsize=9,
                rotation=90,
                color='white'
            )

        ax.set_xlabel("Date")
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d-%b'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        # plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
        plt.setp(
    ax.get_xticklabels(),
    rotation=30,
    ha='right',
    fontweight='semibold',
    fontsize=11,
    color='dimgray',
    fontfamily='sans-serif',
)


    ax.set_ylabel("Generation (kWh)")
    
    # Update title for hourly aggregation
    if is_hourly_aggregation:
        title = f"Hourly Aggregated Generation - {plant_name}\n{start_date} to {end_date}"
    else:
        title = f"Generation - {plant_name}\n{start_date}"
        if not is_single_day:
            title += f" to {end_date}"
    
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.yaxis.set_major_formatter(FuncFormatter(format_thousands))
    ax.grid(True, linestyle='--', alpha=0.6)
    plt.tight_layout()

    return fig



# ##Consumption
def create_consumption_plot(df, plant_name, start_date, end_date=None, is_hourly_aggregation=False):
    fig, ax = plt.subplots(figsize=(12, 6))

    if df.empty or 'consumption' not in df.columns:
        ax.text(0.5, 0.5, 'No consumption data available', ha='center', va='center', fontsize=12, color='red')
        ax.set_title(f"Consumption - {plant_name}", fontsize=14, fontweight='bold')
        return fig

    is_single_day = end_date is None or start_date == end_date

    if is_single_day:
        df['datetime'] = pd.to_datetime(df['datetime'])
        df = df.groupby('datetime', as_index=False)['consumption'].sum()
        ax.plot(df['datetime'], df['consumption'], color='red', marker='o', linewidth=2)
        ax.set_xlabel("Time of Day")
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M'))
        ax.xaxis.set_major_locator(mdates.HourLocator(interval=2))
        title = f"Consumption - {plant_name}\n{start_date}"
    elif is_hourly_aggregation:
        # For hourly aggregation, use hour as x-axis
        bars = ax.bar(df['hour'], df['consumption'], color='red', alpha=0.8)

        # Add values inside bars rotated 90 degrees
        for bar in bars:
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2,
                height * 0.5,
                f"{int(height):,}",
                ha='center',
                va='center',
                fontsize=9,
                rotation=90,
                color='black'
            )

        ax.set_xlabel("Hour of Day (across all selected days)")
        ax.set_xticks(range(0, 24))
        ax.set_xticklabels([f"{h:02d}:00" for h in range(24)])
        # plt.setp(ax.get_xticklabels(), rotation=30, ha='right', fontweight='bold')
        plt.setp(
    ax.get_xticklabels(),
    rotation=30,
    ha='right',
    fontweight='semibold',
    fontsize=11,
    color='dimgray',
    fontfamily='sans-serif',
)

        title = f"Hourly Aggregated Consumption - {plant_name}\n{start_date} to {end_date}"
    else:
        df = df.groupby('date', as_index=False)['consumption'].sum()
        bars = ax.bar(df['date'], df['consumption'], color='red', alpha=0.8)

        # Add values inside bars rotated 90 degrees
        for bar in bars:
            height = bar.get_height()
            ax.text(
                bar.get_x() + bar.get_width() / 2,
                height * 0.5,
                f"{int(height):,}",
                ha='center',
                va='center',
                fontsize=9,
                rotation=90,
                color='black'
            )

        ax.set_xlabel("Date")
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%d-%b'))
        ax.xaxis.set_major_locator(mdates.DayLocator(interval=2))
        title = f"Daily Consumption - {plant_name}\n{start_date}"

    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.set_ylabel("Consumption (kWh)")
    ax.yaxis.set_major_formatter(FuncFormatter(format_thousands))
    ax.grid(True, linestyle='--', alpha=0.6)
    
    # Only rotate labels for date plots, not for hourly
    if not is_hourly_aggregation:
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    plt.tight_layout()

    return fig





def plot_consumption_and_generation_pie(df: pd.DataFrame, plant_display_name: str, start_date_str: str,end_date_str: str):
    """
    Create two pie charts side by side:
    - Consumption distribution by cons_unit
    - Allocated generation distribution by cons_unit
    """
    try:
        logging.info(f"Creating pie charts for {plant_display_name} ({start_date_str} to {end_date_str})")
        
        if df is None or df.empty:
            logging.warning(f"No data available for pie charts - {plant_display_name}")
            return None

        # Validate required columns
        required_columns = ['cons_unit', 'consumption', 'allocated_generation']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            logging.error(f"Missing required columns in pie chart data: {missing_columns}")
            return None
        
    except Exception as e:
        logging.error(f"Error initializing pie charts for {plant_display_name}: {str(e)}")
        return None

    try:
        fig, axes = plt.subplots(1, 2, figsize=(18, 8))

        # Define a comprehensive list of distinct colors
        distinct_colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', 
            '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA', '#F1948A', '#85929E',
            '#D5DBDB', '#AED6F1', '#A9DFBF', '#F9E79F', '#D2B4DE', '#AEB6BF', '#FADBD8',
            '#D1F2EB', '#FCF3CF', '#EBDEF0', '#EAF2F8', '#E8F8F5', '#FEF9E7', '#F4ECF7',
            '#154360', '#0E6655', '#7D6608', '#633974', '#2E86AB', '#A23B72', '#F18F01',
            '#C73E1D', '#2F3061', '#4056A1', '#D79922', '#EFE2BA', '#F13C20', '#4056A1'
        ]
        
        # Aggregate data by cons_unit
        pie_data = df.groupby('cons_unit').agg({
            'consumption': 'sum',
            'allocated_generation': 'sum'
        }).reset_index()

        if pie_data.empty:
            logging.warning(f"No aggregated data available for pie charts - {plant_display_name}")
            return None
            
        logging.info(f"Successfully prepared pie chart data for {plant_display_name}")
        
    except Exception as e:
        logging.error(f"Error preparing pie chart data for {plant_display_name}: {str(e)}")
        return None
    
    # Get all unique cons_units across both datasets
    consumption_data = df.groupby('cons_unit')['consumption'].sum()
    generation_data = df.groupby('cons_unit')['allocated_generation'].sum()
    all_units = list(set(consumption_data.index.tolist() + generation_data.index.tolist()))
    
    # Ensure we have enough colors
    if len(all_units) > len(distinct_colors):
        # Generate additional colors using matplotlib's color cycle
        import matplotlib.cm as cm
        import numpy as np
        additional_colors = cm.Set3(np.linspace(0, 1, len(all_units) - len(distinct_colors)))
        distinct_colors.extend(['#%02x%02x%02x' % (int(r*255), int(g*255), int(b*255)) for r, g, b, a in additional_colors])
    
    # Create color mapping for consistent colors across both charts
    color_map = {unit: distinct_colors[i] for i, unit in enumerate(all_units)}
    
    # Consumption pie with consistent colors
    consumption_colors = [color_map[unit] for unit in consumption_data.index]
    axes[0].pie(
        consumption_data, 
        labels=consumption_data.index,
        colors=consumption_colors,
        autopct='%1.1f%%',
        startangle=140,
        wedgeprops={'edgecolor': 'w', 'linewidth': 1.5}
    )
    # Format date range for title
    date_range = start_date_str if end_date_str is None or start_date_str == end_date_str else f"{start_date_str} to {end_date_str}"
    
    axes[0].set_title(f'Consumption Distribution by unit\n{plant_display_name}\n({date_range})', fontsize=14, fontweight='bold')

    # Allocated generation pie with consistent colors
    generation_colors = [color_map[unit] for unit in generation_data.index]
    axes[1].pie(
        generation_data, 
        labels=generation_data.index,
        colors=generation_colors,
        autopct='%1.1f%%',
        startangle=140,
        wedgeprops={'edgecolor': 'w', 'linewidth': 1.5}
    )
    axes[1].set_title(f'Allocated Generation Distribution by unit\n{plant_display_name}\n({date_range})', fontsize=14, fontweight='bold')

    try:
        plt.tight_layout()
        logging.info(f"Successfully created pie charts for {plant_display_name}")
        return fig
    except Exception as e:
        logging.error(f"Error finalizing pie charts for {plant_display_name}: {str(e)}")
        return fig  # Return figure even if tight_layout fails

import logging
from typing import Optional


logger = logging.getLogger(__name__)

def plot_consumption_and_generation_pie_interactive(
    df: pd.DataFrame,
    plant_display_name: str,
    start_date: str,
    end_date: str
) -> Optional[go.Figure]:
    """
    Create an interactive Plotly figure with side-by-side pie charts for
    consumption and generation distribution across consumer units.

    Args:
        df (pd.DataFrame): Input data containing columns - cons_unit, consumption, allocated_generation.
        plant_display_name (str): Plant display name used in the chart title.
        start_date (str): Start date in 'YYYY-MM-DD' format.
        end_date (str): End date in 'YYYY-MM-DD' format.

    Returns:
        Optional[go.Figure]: Interactive Plotly figure if data is valid; None otherwise.
    """
    # Validate empty DataFrame
    if df.empty:
        logger.warning("No data available for plotting.")
        return None

    # Validate required columns
    required_cols = ['cons_unit', 'consumption', 'allocated_generation']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        logger.error(f"Missing required columns: {missing_cols}")
        return None

    # Aggregate consumption and generation by consumer unit
    consumption_df = df.groupby('cons_unit', as_index=False)['consumption'].sum()
    generation_df = df.groupby('cons_unit', as_index=False)['allocated_generation'].sum()

    # Filter out rows with zero totals
    consumption_df = consumption_df[consumption_df['consumption'] > 0]
    generation_df = generation_df[generation_df['allocated_generation'] > 0]

    if consumption_df.empty and generation_df.empty:
        logger.warning("All consumption and generation data have zero values; nothing to plot.")
        return None

    # Prepare figure layout with two pie charts
    fig = make_subplots(
        rows=1, cols=2,
        specs=[[{"type": "pie"}, {"type": "pie"}]],
        subplot_titles=[
            "Consumption Distribution by Consumer Unit",
            "Generation Distribution by Consumer Unit"
        ],
        horizontal_spacing=0.15
    )

    # Define color palette and mapping
    color_palette = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', 
        '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA',
        '#F1948A', '#85929E', '#D5DBDB', '#AED6F1', '#A9DFBF', '#F9E79F',
        '#D2B4DE', '#AEB6BF', '#FADBD8', '#D1F2EB', '#FCF3CF', '#EBDEF0'
    ]

    all_units = list(set(consumption_df['cons_unit']).union(generation_df['cons_unit']))
    color_map = {unit: color_palette[i % len(color_palette)] for i, unit in enumerate(all_units)}

    # Helper for creating pie chart traces
    def create_pie_trace(data: pd.DataFrame, value_col: str, title: str, total: float) -> go.Pie:
        data['percentage'] = (data[value_col] / total * 100).round(1)
        hover_text = [
            f"<b>🏢 Unit:</b> {row['cons_unit']}<br>"
            f"<b>{title}:</b> {row[value_col]:,.0f} kWh<br>"
            f"<b>📊 Share:</b> {row['percentage']:.1f}%<br>"
            f"<b>📈 Total:</b> {total:,.0f} kWh"
            for _, row in data.iterrows()
        ]
        return go.Pie(
            labels=data['cons_unit'],
            values=data[value_col],
            marker=dict(
                colors=[color_map[unit] for unit in data['cons_unit']],
                line=dict(color='white', width=2)
            ),
            hovertext=hover_text,
            hovertemplate='%{hovertext}<extra></extra>',
            textinfo='label+percent',
            textposition='auto',
            textfont=dict(size=11),
            name=title
        )

    # Add consumption pie chart
    if not consumption_df.empty:
        total_consumption = consumption_df['consumption'].sum()
        fig.add_trace(
            create_pie_trace(consumption_df, 'consumption', 'Consumption', total_consumption),
            row=1, col=1
        )

    # Add generation pie chart
    if not generation_df.empty:
        total_generation = generation_df['allocated_generation'].sum()
        fig.add_trace(
            create_pie_trace(generation_df, 'allocated_generation', 'Generation', total_generation),
            row=1, col=2
        )

    # Prepare date range for subtitle
    date_range = start_date if start_date == end_date else f"{start_date} to {end_date}"

    # Configure figure layout
    fig.update_layout(
        title={
            'text': f"Energy Distribution by Consumer Unit - {plant_display_name} ({date_range})",
            'x': 0.5, 'xanchor': 'center',
            'font': {'size': 16, 'family': 'Arial, sans-serif'}
        },
        font=dict(size=12, family='Arial, sans-serif'),
        legend=dict(
            orientation="v",
            yanchor="middle",
            y=0.5,
            xanchor="left",
            x=1.02,
            font=dict(size=11)
        ),
        margin=dict(l=20, r=150, t=80, b=20),
        height=600,
        hovermode='closest'
    )

    # Enhance subplot title annotations
    fig.update_annotations(
        font=dict(size=14, family='Arial, sans-serif', color='#2E86AB')
    )

    return fig



def create_merged_consumption_generation_table(
    df: pd.DataFrame, 
    plant_display_name: str
) -> pd.DataFrame:
    """
    Create a merged table combining consumption, generation, and location data.
    
    Args:
        df: DataFrame with columns - cons_unit, location_name, consumption, 
            allocated_generation, consumption_percentage, generation_percentage
        plant_display_name: Name of the plant for display purposes
    
    Returns:
        Formatted DataFrame for display
    """
    if df.empty:
        return pd.DataFrame()
    
    # Create a copy of the dataframe for formatting
    display_df = df.copy()
    
    # Format numeric columns for display
    display_df['consumption_formatted'] = display_df['consumption'].apply(lambda x: f"{x:,.0f}")
    display_df['allocated_generation_formatted'] = display_df['allocated_generation'].apply(lambda x: f"{x:,.0f}")
    display_df['consumption_percentage_formatted'] = display_df['consumption_percentage'].apply(lambda x: f"{x:.1f}%")
    display_df['generation_percentage_formatted'] = display_df['generation_percentage'].apply(lambda x: f"{x:.1f}%")
    
    # Select and rename columns for final display
    final_df = display_df[[
        'cons_unit', 
        'location_name',
        'consumption_formatted', 
        'consumption_percentage_formatted',
        'allocated_generation_formatted', 
        'generation_percentage_formatted'
    ]].copy()
    
    # Rename columns for display
    final_df.columns = [
        'Consumer Unit',
        'Location',
        'Consumption (kWh)',
        'Consumption %',
        'Generation (kWh)', 
        'Generation %'
    ]
    
    return final_df




