2025-07-08 12:02:36,753 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:36,753 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 12:02:36,950 - WARNING - summary_display - summary_display.py:181 - No chart data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 12:02:36,950 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 12:02:36,950 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 12:02:37,235 - WARNING - summary_display - summary_display.py:273 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 12:02:37,237 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 12:02:37,435 - WARNING - summary_display - summary_display.py:504 - No unitwise data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 12:02:44,933 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-01
2025-07-08 12:02:44,933 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 12:02:45,149 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 12:02:45,215 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 12:02:45,468 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 12:02:45,468 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 29.77 MWh, Consumption: 22.91 MWh
2025-07-08 12:02:45,477 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-01, hourly_aggregation: False
2025-07-08 12:02:45,477 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 12:02:45,674 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 1056 records
2025-07-08 12:02:46,189 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:02:46,189 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 12:02:46,717 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 30 records
2025-07-08 12:02:46,750 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 12:02:47,276 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 30 records
2025-07-08 12:02:47,281 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 813.46 MWh, Consumption: 687.42 MWh
2025-07-08 12:02:47,281 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-08 12:02:47,281 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 12:02:47,799 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 12:02:47,855 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 12:02:47,857 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:02:48,541 - INFO - summary_display - summary_display.py:429 - Successfully fetched unitwise data with location, 11 records
2025-07-08 12:02:48,541 - INFO - summary_display - summary_display.py:434 - Interactive plot setting: True
2025-07-08 12:02:49,015 - INFO - summary_display - summary_display.py:449 - Successfully fetched pie chart data with 11 records
2025-07-08 12:02:49,056 - INFO - summary_display - summary_display.py:462 - Successfully displayed interactive pie chart
2025-07-08 12:02:49,056 - INFO - summary_display - summary_display.py:495 - Successfully displayed merged table with 11 rows
2025-07-08 12:28:13,417 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:13,418 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 12:28:13,653 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 30 records
2025-07-08 12:28:13,687 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 12:28:13,892 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 30 records
2025-07-08 12:28:13,894 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 813.46 MWh, Consumption: 687.42 MWh
2025-07-08 12:28:13,901 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-08 12:28:13,901 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 12:28:14,109 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 12:28:14,149 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 12:28:14,152 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:14,450 - INFO - summary_display - summary_display.py:429 - Successfully fetched unitwise data with location, 11 records
2025-07-08 12:28:14,450 - INFO - summary_display - summary_display.py:434 - Interactive plot setting: True
2025-07-08 12:28:14,629 - INFO - summary_display - summary_display.py:449 - Successfully fetched pie chart data with 11 records
2025-07-08 12:28:14,650 - INFO - summary_display - summary_display.py:462 - Successfully displayed interactive pie chart
2025-07-08 12:28:14,654 - INFO - summary_display - summary_display.py:495 - Successfully displayed merged table with 11 rows
2025-07-08 12:28:25,581 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:25,581 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 12:28:25,929 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 30 records
2025-07-08 12:28:26,063 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 12:28:26,384 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 30 records
2025-07-08 12:28:26,384 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 813.46 MWh, Consumption: 687.42 MWh
2025-07-08 12:28:26,394 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-08 12:28:26,397 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 12:28:26,729 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 12:28:26,813 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 12:28:26,813 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 12:28:27,162 - INFO - summary_display - summary_display.py:429 - Successfully fetched unitwise data with location, 11 records
2025-07-08 12:28:27,162 - INFO - summary_display - summary_display.py:434 - Interactive plot setting: True
2025-07-08 12:28:27,350 - INFO - summary_display - summary_display.py:449 - Successfully fetched pie chart data with 11 records
2025-07-08 12:28:27,379 - INFO - summary_display - summary_display.py:462 - Successfully displayed interactive pie chart
2025-07-08 12:28:27,389 - INFO - summary_display - summary_display.py:495 - Successfully displayed merged table with 11 rows
2025-07-08 14:54:06,042 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:06,042 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 14:54:06,131 - WARNING - summary_display - summary_display.py:181 - No chart data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 14:54:06,147 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 14:54:06,147 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 14:54:06,253 - WARNING - summary_display - summary_display.py:273 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 14:54:06,253 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 14:54:06,383 - WARNING - summary_display - summary_display.py:504 - No unitwise data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 14:54:11,385 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-01
2025-07-08 14:54:11,385 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 14:54:11,497 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 14:54:11,613 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 14:54:11,768 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 14:54:11,769 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 29.77 MWh, Consumption: 22.91 MWh
2025-07-08 14:54:11,776 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-01, hourly_aggregation: False
2025-07-08 14:54:11,776 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 14:54:11,895 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 1056 records
2025-07-08 14:54:11,941 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 14:54:11,947 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-01
2025-07-08 14:54:12,040 - INFO - summary_display - summary_display.py:429 - Successfully fetched unitwise data with location, 11 records
2025-07-08 14:54:12,040 - INFO - summary_display - summary_display.py:434 - Interactive plot setting: True
2025-07-08 14:54:12,134 - INFO - summary_display - summary_display.py:449 - Successfully fetched pie chart data with 11 records
2025-07-08 14:54:12,170 - INFO - summary_display - summary_display.py:462 - Successfully displayed interactive pie chart
2025-07-08 14:54:12,177 - INFO - summary_display - summary_display.py:495 - Successfully displayed merged table with 11 rows
2025-07-08 14:54:12,355 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 14:54:12,355 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 14:54:12,646 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 30 records
2025-07-08 14:54:12,818 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 14:54:13,034 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 30 records
2025-07-08 14:54:13,034 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 813.46 MWh, Consumption: 687.42 MWh
2025-07-08 14:54:13,041 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30, hourly_aggregation: True
2025-07-08 14:54:13,041 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 14:54:13,252 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 14:54:13,318 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 14:54:13,332 - INFO - summary_display - summary_display.py:415 - Displaying consumption and generation pie chart for plant: Kids Clinic India Limited, dates: 2025-04-01 to 2025-04-30
2025-07-08 14:54:13,602 - INFO - summary_display - summary_display.py:429 - Successfully fetched unitwise data with location, 11 records
2025-07-08 14:54:13,602 - INFO - summary_display - summary_display.py:434 - Interactive plot setting: True
2025-07-08 14:54:13,762 - INFO - summary_display - summary_display.py:449 - Successfully fetched pie chart data with 11 records
2025-07-08 14:54:13,811 - INFO - summary_display - summary_display.py:462 - Successfully displayed interactive pie chart
2025-07-08 14:54:13,812 - INFO - summary_display - summary_display.py:495 - Successfully displayed merged table with 11 rows
2025-07-08 15:48:10,662 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 15:48:10,662 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:48:10,757 - WARNING - summary_display - summary_display.py:181 - No chart data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 15:48:12,810 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08, hourly_aggregation: False
2025-07-08 15:48:12,810 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 15:48:12,920 - WARNING - summary_display - summary_display.py:273 - No hourly data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 15:48:13,899 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-01
2025-07-08 15:48:13,900 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:48:14,125 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 15:48:14,269 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 15:48:14,381 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 15:48:14,381 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 27.38 MWh, Consumption: 21.14 MWh
2025-07-08 15:48:14,802 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:48:14,804 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:48:15,187 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 15:48:15,237 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 15:48:15,536 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 15:48:15,552 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 15:48:18,304 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 15:48:18,304 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 15:48:18,537 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 15:48:19,121 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 15:50:57,185 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:50:57,185 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:50:57,379 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 15:50:57,420 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 15:50:57,615 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 15:50:57,615 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 15:50:59,133 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 15:50:59,134 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 15:50:59,366 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 15:50:59,415 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 15:51:15,507 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:51:15,508 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:51:15,751 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 15:51:15,788 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 15:51:15,984 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 15:51:15,984 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 15:51:17,507 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 15:51:17,508 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 15:51:17,741 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 15:51:17,783 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 15:53:06,336 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 15:53:06,337 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 15:53:06,594 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 15:53:06,632 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 15:53:06,910 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 15:53:06,910 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 15:53:08,382 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 15:53:08,382 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 15:53:08,644 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 15:53:08,694 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 16:03:09,043 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:03:09,043 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:03:09,260 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 16:03:09,297 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:03:09,549 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 16:03:09,549 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:03:11,131 - INFO - summary_display - summary_display.py:215 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:03:11,131 - INFO - summary_display - summary_display.py:220 - Interactive plot setting: True
2025-07-08 16:03:11,390 - INFO - summary_display - summary_display.py:235 - Successfully fetched hourly data with 24 records
2025-07-08 16:03:11,426 - INFO - summary_display - summary_display.py:249 - Successfully displayed interactive hourly chart
2025-07-08 16:04:53,516 - INFO - summary_display - summary_display.py:332 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:04:53,516 - INFO - summary_display - summary_display.py:337 - Interactive plot setting: True
2025-07-08 16:04:53,753 - INFO - summary_display - summary_display.py:352 - Successfully fetched hourly data with 24 records
2025-07-08 16:04:53,810 - INFO - summary_display - summary_display.py:366 - Successfully displayed interactive hourly chart
2025-07-08 16:05:50,237 - INFO - summary_display - summary_display.py:332 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:05:50,238 - INFO - summary_display - summary_display.py:337 - Interactive plot setting: True
2025-07-08 16:05:50,509 - INFO - summary_display - summary_display.py:352 - Successfully fetched hourly data with 24 records
2025-07-08 16:05:50,549 - INFO - summary_display - summary_display.py:366 - Successfully displayed interactive hourly chart
2025-07-08 16:07:18,767 - INFO - summary_display - summary_display.py:336 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:07:18,767 - INFO - summary_display - summary_display.py:341 - Interactive plot setting: True
2025-07-08 16:07:19,056 - INFO - summary_display - summary_display.py:356 - Successfully fetched hourly data with 24 records
2025-07-08 16:07:19,095 - INFO - summary_display - summary_display.py:370 - Successfully displayed interactive hourly chart
2025-07-08 16:07:50,688 - INFO - summary_display - summary_display.py:340 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:07:50,688 - INFO - summary_display - summary_display.py:345 - Interactive plot setting: True
2025-07-08 16:07:51,074 - INFO - summary_display - summary_display.py:360 - Successfully fetched hourly data with 24 records
2025-07-08 16:07:51,130 - INFO - summary_display - summary_display.py:374 - Successfully displayed interactive hourly chart
2025-07-08 16:07:54,554 - INFO - summary_display - summary_display.py:341 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:07:54,554 - INFO - summary_display - summary_display.py:346 - Interactive plot setting: True
2025-07-08 16:07:54,791 - INFO - summary_display - summary_display.py:361 - Successfully fetched hourly data with 24 records
2025-07-08 16:07:54,831 - INFO - summary_display - summary_display.py:375 - Successfully displayed interactive hourly chart
2025-07-08 16:08:30,816 - INFO - summary_display - summary_display.py:341 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:08:30,816 - INFO - summary_display - summary_display.py:346 - Interactive plot setting: True
2025-07-08 16:08:31,023 - INFO - summary_display - summary_display.py:361 - Successfully fetched hourly data with 24 records
2025-07-08 16:08:31,064 - INFO - summary_display - summary_display.py:375 - Successfully displayed interactive hourly chart
2025-07-08 16:09:44,161 - INFO - summary_display - summary_display.py:345 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:09:44,162 - INFO - summary_display - summary_display.py:350 - Interactive plot setting: True
2025-07-08 16:09:44,435 - INFO - summary_display - summary_display.py:365 - Successfully fetched hourly data with 24 records
2025-07-08 16:09:44,494 - INFO - summary_display - summary_display.py:379 - Successfully displayed interactive hourly chart
2025-07-08 16:10:37,047 - INFO - summary_display - summary_display.py:221 - Displaying monthly energy data for None: 2024-07-01 to 2025-07-08
2025-07-08 16:10:38,739 - INFO - summary_display - summary_display.py:345 - Displaying hourly generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31, hourly_aggregation: True
2025-07-08 16:10:38,739 - INFO - summary_display - summary_display.py:350 - Interactive plot setting: True
2025-07-08 16:10:39,053 - INFO - summary_display - summary_display.py:365 - Successfully fetched hourly data with 24 records
2025-07-08 16:10:39,096 - INFO - summary_display - summary_display.py:379 - Successfully displayed interactive hourly chart
2025-07-08 16:12:02,961 - INFO - summary_display - summary_display.py:221 - Displaying monthly energy data for None: 2024-07-01 to 2025-07-08
2025-07-08 16:12:35,573 - INFO - summary_display - summary_display.py:221 - Displaying monthly energy data for Kids Clinic India Limited: 2024-07-01 to 2025-07-08
2025-07-08 16:12:35,946 - ERROR - summary_display - summary_display.py:315 - Critical error in display_generation_vs_consumption: time data "%Y-%m" doesn't match format "%Y-%m", at position 0. You might want to try:
    - passing `format` if your strings have a consistent format;
    - passing `format='ISO8601'` if your strings are all ISO8601 but not necessarily in exactly the same format;
    - passing `format='mixed'`, and the format will be inferred for each element individually. You might want to use `dayfirst` alongside this.
2025-07-08 16:19:02,437 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-01
2025-07-08 16:19:02,438 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:19:02,576 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 16:19:02,607 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:19:02,726 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 16:19:02,726 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 27.38 MWh, Consumption: 21.14 MWh
2025-07-08 16:19:20,256 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-01
2025-07-08 16:19:20,256 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:19:20,405 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 16:19:20,427 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:19:20,561 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 16:19:20,562 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 27.38 MWh, Consumption: 21.14 MWh
2025-07-08 16:19:36,597 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-01
2025-07-08 16:19:36,598 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:19:36,745 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 1056 records
2025-07-08 16:19:36,830 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:19:36,968 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 1056 records
2025-07-08 16:19:36,969 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 27.38 MWh, Consumption: 21.14 MWh
2025-07-08 16:20:41,154 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:20:41,154 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:20:41,366 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 16:20:41,408 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:20:41,700 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 16:20:41,700 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:30:00,835 - INFO - summary_display - summary_display.py:51 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-07-08 to 2025-07-08
2025-07-08 16:30:00,836 - INFO - summary_display - summary_display.py:56 - Interactive plot setting: True
2025-07-08 16:30:00,942 - WARNING - summary_display - summary_display.py:183 - No chart data available for plant Kids Clinic India Limited between 2025-07-08 and 2025-07-08
2025-07-08 16:30:04,424 - INFO - summary_display - summary_display.py:51 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-01
2025-07-08 16:30:04,424 - INFO - summary_display - summary_display.py:56 - Interactive plot setting: True
2025-07-08 16:30:04,526 - INFO - summary_display - summary_display.py:66 - Successfully fetched chart data with 1056 records
2025-07-08 16:30:04,841 - INFO - summary_display - summary_display.py:81 - Successfully displayed interactive chart
2025-07-08 16:30:04,952 - INFO - summary_display - summary_display.py:109 - Successfully fetched metrics data with 1056 records
2025-07-08 16:30:04,953 - INFO - summary_display - summary_display.py:130 - Calculated metrics - Generation: 27.38 MWh, Consumption: 21.14 MWh
2025-07-08 16:30:05,621 - INFO - summary_display - summary_display.py:51 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:30:05,621 - INFO - summary_display - summary_display.py:56 - Interactive plot setting: True
2025-07-08 16:30:05,859 - INFO - summary_display - summary_display.py:66 - Successfully fetched chart data with 31 records
2025-07-08 16:30:05,919 - INFO - summary_display - summary_display.py:81 - Successfully displayed interactive chart
2025-07-08 16:30:06,205 - INFO - summary_display - summary_display.py:109 - Successfully fetched metrics data with 31 records
2025-07-08 16:30:06,214 - INFO - summary_display - summary_display.py:130 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
2025-07-08 16:31:47,329 - INFO - summary_display - summary_display.py:523 - Displaying monthly FY supplied vs consumed for plant: Kids Clinic India Limited
2025-07-08 16:31:47,723 - INFO - summary_display - summary_display.py:530 - Successfully fetched monthly FY data with 6 months
2025-07-08 16:31:47,807 - INFO - summary_display - summary_display.py:577 - Successfully displayed monthly FY chart
2025-07-08 16:32:18,141 - INFO - summary_display - summary_display.py:49 - Displaying generation vs consumption for plant: Kids Clinic India Limited, dates: 2025-05-01 to 2025-05-31
2025-07-08 16:32:18,141 - INFO - summary_display - summary_display.py:54 - Interactive plot setting: True
2025-07-08 16:32:18,422 - INFO - summary_display - summary_display.py:64 - Successfully fetched chart data with 31 records
2025-07-08 16:32:18,463 - INFO - summary_display - summary_display.py:79 - Successfully displayed interactive chart
2025-07-08 16:32:18,801 - INFO - summary_display - summary_display.py:107 - Successfully fetched metrics data with 31 records
2025-07-08 16:32:18,802 - INFO - summary_display - summary_display.py:128 - Calculated metrics - Generation: 569.30 MWh, Consumption: 640.15 MWh
